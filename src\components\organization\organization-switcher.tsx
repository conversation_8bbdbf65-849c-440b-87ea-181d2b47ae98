"use client";

import * as React from "react";
import { ChevronsUpDown, Plus, AlertCircle } from "lucide-react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import { useEffect } from 'react';
// Removed event bus imports - now using Zustand directly
import { useAuthContextStore } from "@/stores/useAuthContextStore";
import { cn } from "@/lib/utils";
import { toastMessages } from "@/lib/toast-messages";
import { useOrganizationStorage } from "@/hooks/use-organization-storage";
import { OrganizationSwitcherProps } from "@/types/components/organization/OrganizationSwitcherProps";
import { createClient } from "@/lib/supabase/client";
import { evaluateRbac } from "@/lib/rbac/rbac-utils";
import { useOrganizationsList } from "@/hooks/use-organizations-list";

import type { Organization } from "@/types/organization";
import { roleUtils } from "@/lib/rbac/role-utils";
import { evaluateUnifiedAccess } from "@/lib/navigation/route-access-evaluator";
// Removed direct event bus imports as we now rely on the central event handler

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

// Global indicator ID - must match the one in dashboard-event-manager-core.tsx
const GLOBAL_INDICATOR_ID = 'global-event-system-indicator';

/**
 * Checks if the dashboard event manager is available using the global indicator
 * This is more reliable than the previous DOM query approach
 */
function isDashboardEventManagerAvailable(): boolean {
  if (typeof window === 'undefined') return false;

  const indicator = document.getElementById(GLOBAL_INDICATOR_ID);
  if (!indicator) return false;

  // Check both data-status and data-dashboard-event-manager attributes
  const status = indicator.getAttribute('data-status');
  const managerStatus = indicator.getAttribute('data-dashboard-event-manager');

  // Event manager is available if either status is 'ready' or manager status is 'active'
  return status === 'ready' || managerStatus === 'active' || managerStatus === 'true';
}

export function OrganizationSwitcher({
  organizations: organizationsProp,
  activeOrganization,
  className,
}: OrganizationSwitcherProps) {
  const router = useRouter();
  const supabase = createClient();
  const [isOpen, setIsOpen] = React.useState(false);
  const [isSwitching, setIsSwitching] = React.useState(false);
  const [selectedOrg, setSelectedOrg] = React.useState<Organization | null>(
    activeOrganization
  );
  // Track if we're in an active organizatin switch to prevent feedback loops
  const activeSwitchRef = React.useRef(false);

  // Get loading state from auth store for hydration guard
  const isStoreLoading = useAuthContextStore(state => state.isLoading);

  // Use the organizations list hook with initialData but don't revalidate on mount
  const {
    organizations: fetchedOrganizations,
    refreshOrganizations,
  } = useOrganizationsList({
    initialData: organizationsProp, // Use the prop data as initial data to avoid flickering
  });

  // Initialize localOrgs with either organizationsProp, activeOrganization, or empty array
  const [localOrgs, setLocalOrgs] = React.useState<Organization[]>(() => {
    if (organizationsProp && organizationsProp.length > 0) {
      return organizationsProp;
    } else if (activeOrganization) {
      return [activeOrganization];
    }
    return [];
  });

  const previousOrgIdRef = React.useRef<string | null>(null);

  // Check for event manager existence
  useEffect(() => {
    // Check for dashboard event manager on mount to coordinate with realtime updates
    if (typeof window !== 'undefined') {
      const eventManagerExists = isDashboardEventManagerAvailable();
      console.log(`[OrganizationSwitcher] Initialized with event manager present: ${eventManagerExists}`);
    }
  }, []);

  // Get the user ID on mount - This useEffect is now only for logging or other side effects not related to userId state
  React.useEffect(() => {
    const getUserId = async () => {
      const { data: { user } } = await supabase.auth.getUser();
      if (user) {
        // userId state was removed, this is just an example of where it was
      }
    };

    getUserId();
  }, [supabase]);

  // Use the organization storage hook to persist the active organization
  useOrganizationStorage(activeOrganization, localOrgs);

  // Update selected org when activeOrganization changes and it's different from the previous one
  React.useEffect(() => {
    if (activeOrganization && activeOrganization.id !== previousOrgIdRef.current) {
      setSelectedOrg(prev => {
        // Only update if the org is different to prevent unnecessary re-renders
        if (!prev || prev.id !== activeOrganization.id) {
          previousOrgIdRef.current = activeOrganization.id;
          return activeOrganization;
        }
        return prev;
      });
    }
  }, [activeOrganization]);

  // Update local organizations when the prop changes for backward compatibility
  React.useEffect(() => {
    // Skip everything if organizationsProp is empty or not an array
    if (!organizationsProp || !Array.isArray(organizationsProp) || organizationsProp.length === 0) {
      if (fetchedOrganizations && fetchedOrganizations.length > 0) {
        // If props are empty but we have fetched organizations, use those
        setLocalOrgs(fetchedOrganizations);
      } else if (activeOrganization && localOrgs.length === 0) {
        // If we have no orgs at all but have an active org, at least show that
        setLocalOrgs([activeOrganization]);
      }
      // Don't set to empty array here, as we might have valid organizations in local state
      return;
    }

    // Avoid redundant updates by doing a deeper comparison of the data
    const hasChanged = localOrgs.length !== organizationsProp.length ||
      localOrgs.some((localOrg, i) => {
        const propOrg = organizationsProp[i];
        if (!propOrg) return true; // Different lengths

        // Compare the critical properties
        return localOrg.id !== propOrg.id ||
               localOrg.name !== propOrg.name ||
               localOrg.org_member_role !== propOrg.org_member_role ||
               localOrg.org_member_is_active !== propOrg.org_member_is_active ||
               localOrg.isActive !== propOrg.isActive;
      });

    if (hasChanged) {
      setLocalOrgs(organizationsProp);
    }
  }, [organizationsProp, fetchedOrganizations, localOrgs, activeOrganization]);

  // Refresh organizations ONLY when dropdown is opened
  React.useEffect(() => {
    if (isOpen) {
      console.log('[OrganizationSwitcher] Dropdown opened, refreshing organizations');
      // Small delay to avoid hammering the API if user rapidly opens/closes
      const refreshTimeout = setTimeout(() => {
        refreshOrganizations().then(updatedOrgs => {
          if (updatedOrgs && updatedOrgs.length > 0) {
            setLocalOrgs(updatedOrgs);
          }
        });
      }, 200);

      return () => clearTimeout(refreshTimeout);
    }
  }, [isOpen, refreshOrganizations]);

  // Use fetched organizations when they become available
  React.useEffect(() => {
    if (fetchedOrganizations && fetchedOrganizations.length > 0) {
      setLocalOrgs(() => {
        // Check if the selectedOrg is still in the fetched organizations
        const selectedOrgStillExists = selectedOrg && fetchedOrganizations.some(
          org => org.id === selectedOrg.id
        );

        // If selectedOrg is not in the fetched organizations, update it to the first org
        if (selectedOrg && !selectedOrgStillExists && fetchedOrganizations.length > 0) {
          const newSelectedOrg = fetchedOrganizations[0];
          setSelectedOrg(newSelectedOrg);
          previousOrgIdRef.current = newSelectedOrg.id;
        }

        // Make sure we don't lose the active organization if it's not in the fetched list
        if (activeOrganization) {
          const activeOrgInFetched = fetchedOrganizations.some(
            org => org.id === activeOrganization.id
          );

          if (!activeOrgInFetched) {
            return [...fetchedOrganizations, activeOrganization];
          }
        }

        return fetchedOrganizations;
      });
    }
  }, [fetchedOrganizations, activeOrganization, selectedOrg]);

  const switchOrganization = React.useCallback(async (orgId: string) => {
    if (!orgId || isSwitching) return;

    try {
      // Mark that we're in an active switch - this prevents the subscription feedback loop
      activeSwitchRef.current = true;
      setIsSwitching(true);

      // Mark this tab as the initiator to ignore subsequent events
      const switchId = `switch_${Date.now()}_${Math.random()}`;
      sessionStorage.setItem('currentOrgSwitchId', switchId);

      console.log('[OrgSwitcher] Switching to organization:', orgId);

      // Find the new organization from the list for UI updates
      const newOrgFromList = localOrgs.find((org) => org.id === orgId);
      if (!newOrgFromList) {
        throw new Error("Organization not found");
      }

      // Update UI immediately for better UX
      setSelectedOrg(newOrgFromList);
      previousOrgIdRef.current = newOrgFromList.id;

      // Close dropdown immediately
      setIsOpen(false);

      // ==== STEP 1: GET FRESH DATA FOR CRITICAL PERMISSION EVALUATION ====
      // Always fetch fresh organization data for permission decisions to avoid stale data issues
      console.log('[OrgSwitcher] Fetching fresh organization data for permission evaluation');
      const freshOrgResponse = await fetch('/api/organizations/authorized', {
        method: 'GET',
        cache: 'no-store'
      });

      if (!freshOrgResponse.ok) {
        throw new Error("Failed to fetch fresh organization data");
      }

      const freshOrganizations = await freshOrgResponse.json();
      const freshOrg = freshOrganizations.find((org: any) => org.id === orgId);

      if (!freshOrg) {
        throw new Error("Organization not found or access denied");
      }

      console.log('[OrgSwitcher] Fresh organization data retrieved:', {
        orgId: freshOrg.id,
        orgName: freshOrg.name,
        userRole: freshOrg.org_member_role,
        isUserActive: freshOrg.org_member_is_active,
        isOrgActive: freshOrg.isActive,
        dataSource: 'fresh_api_call'
      });

      const getCurrentPath = (): string => {
        if (typeof window !== 'undefined') {
          return window.location.pathname;
        }
        return '/dashboard';
      };

      const currentPath = getCurrentPath();
      const unifiedResult = evaluateUnifiedAccess({
        currentPath,
        userRoleId: freshOrg.org_member_role ?? 0,
        isUserActiveInOrg: freshOrg.org_member_is_active !== false,
        isOrgActive: freshOrg.isActive !== false,
        isSuperAdmin: evaluateRbac(freshOrg.org_member_role ?? 0, { rRoles: ['superAdmin'] })
      });

      console.log('[OrgSwitcher] Pre-switch access evaluation:', {
        currentPath,
        userRole: freshOrg.org_member_role ?? 0,
        isUserActiveInOrg: freshOrg.org_member_is_active !== false,
        isOrgActive: freshOrg.isActive !== false,
        canAccess: unifiedResult.canAccess,
        redirectTo: unifiedResult.redirectTo,
        priority: unifiedResult.priority,
        reason: unifiedResult.reason
      });

      // ==== STEP 2: UPDATE STORE WITH FRESH DATA ====
      const currentState = useAuthContextStore.getState();
      const newContext = {
        userId: currentState.userId,
        orgId: freshOrg.id,
        roleId: freshOrg.org_member_role ?? 0,
        isUserActiveInOrg: freshOrg.org_member_is_active !== false,
        isOrgActive: freshOrg.isActive !== false,
        activeOrgName: freshOrg.name,
        userEmail: currentState.userEmail,
        userFullName: currentState.userFullName,
        avatarUrl: currentState.avatarUrl,
      };

      console.log('[OrgSwitcher] Updating store with fresh organization data:', newContext);
      useAuthContextStore.getState().updateFullContext(newContext);

      // Set optimistic navigation flag
      useAuthContextStore.getState().setOptimisticNavigation(true);
      console.log('[OrgSwitcher] Set optimisticNavigation to true in Zustand store');

      // ==== STEP 3: CALL SERVER ACTION TO UPDATE DATABASE ====
      console.log('[OrgSwitcher] Calling server action to update organization in database');
      const result = await import('@/app/actions/switch-organization').then(mod =>
        mod.switchOrganization(orgId)
      );

      if (!result || result.success === false) {
        throw new Error(result?.error || "Failed to switch organization");
      }

      console.log('[OrgSwitcher] Server confirmed database update');

      // ==== STEP 4: HANDLE REDIRECT SCENARIOS ====
      if (!unifiedResult.canAccess) {
        console.log('[OrgSwitcher] Access denied or redirect required - redirecting to:', unifiedResult.redirectTo, 'Priority:', unifiedResult.priority, 'Reason:', unifiedResult.reason);

        // Redirect after database update is complete
        router.push(unifiedResult.redirectTo);
      } else {
        console.log('[OrgSwitcher] User has access, staying on current page');

        // Show optimistic success toast for normal switches
        toastMessages.organization.switchSuccess();
      }

      console.log('[OrgSwitcher] Organization switch complete, data will refresh automatically');

      // Reset the optimisticNavigation flag after a small delay
      setTimeout(() => {
        useAuthContextStore.getState().setOptimisticNavigation(false);
        console.log('[OrgSwitcher] Reset optimisticNavigation to false in Zustand store');
      }, 500);

      // Clear the switch ID after a delay to allow events to settle
      setTimeout(() => {
        sessionStorage.removeItem('currentOrgSwitchId');
        console.log('[OrgSwitcher] Cleared switch ID, will now process external events');
      }, 2000); // 2 second delay to ignore immediate feedback events

    } catch (error) {
      console.error("Error switching organization:", error);

      toastMessages.organization.switchError(
        error instanceof Error ? error.message : undefined
      );

      if (activeOrganization) {
        setSelectedOrg(activeOrganization);
        previousOrgIdRef.current = activeOrganization.id;
      }
    } finally {
      // Reset switching state regardless of success/failure
      setIsSwitching(false);

      // Reset the active switch flag with a small delay to ensure all callbacks complete
      setTimeout(() => {
        activeSwitchRef.current = false;
      }, 100);

      // Clear the switch ID in case of error (success case is handled above)
      setTimeout(() => {
        sessionStorage.removeItem('currentOrgSwitchId');
      }, 2000);
    }
  }, [isSwitching, localOrgs, activeOrganization, router]); // Added supabase to dependency array for switchOrganization

  // Listen for organization switch completion
  React.useEffect(() => {
    const handleSwitchCompleted = () => {
      console.log('[OrgSwitcher] Received switch completion signal, clearing switching flag');
      setIsSwitching(false);
    };

    window.addEventListener('orgSwitchCompleted', handleSwitchCompleted);
    return () => window.removeEventListener('orgSwitchCompleted', handleSwitchCompleted);
  }, []);

  // Sync with the Zustand store when it changes
  React.useEffect(() => {
    // Subscribe to the store
    const unsubscribe = useAuthContextStore.subscribe(
      // Select multiple fields from the store
      (state) => ({
        orgId: state.orgId,
        roleId: state.roleId,
        activeOrgName: state.activeOrgName,
        isUserActiveInOrg: state.isUserActiveInOrg,
        isOrgActive: state.isOrgActive,
      }),
      (currentStateFromStore) => {
        // Skip updates during an active manual switch to prevent feedback loops
        if (activeSwitchRef.current || isSwitching) {
          console.log('[OrgSwitcher] Store update skipped during active manual switch (complex state).');
          return;
        }

        // Skip updates if user has been redirected for current route
        if (typeof window !== 'undefined') {
          const currentPath = window.location.pathname;
          const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
          const redirectFlag = sessionStorage.getItem(redirectKey);
          if (redirectFlag) {
            console.log('[OrgSwitcher] Store update skipped - redirect in progress for route:', currentPath);
            return;
          }
        }

        const { orgId, roleId, activeOrgName, isUserActiveInOrg, isOrgActive } = currentStateFromStore;

        if (!orgId) {
          // No active org in store. If a selectedOrg exists, we might want to clear it or handle as appropriate.
          // For now, if no orgId, we don't change selectedOrg, assuming another mechanism handles this (e.g. logout)
          // Consider: if (selectedOrg !== null) setSelectedOrg(null);
          return;
        }

        const newRoleName = roleId ? roleUtils.getRoleName(roleId as any) : 'Member'; // Default role name

        setSelectedOrg(prevSelectedOrg => {
          const orgFromLocalList = localOrgs.find(o => o.id === orgId);

          const newSelectedOrgData: Organization = {
            ...(orgFromLocalList || {}), // Base on local data if available (for icon, etc.)
            id: orgId, // From store
            name: activeOrgName || orgFromLocalList?.name || 'Loading...', // Prioritize store name
            org_member_role: roleId ?? 0, // From store, provide default
            role: newRoleName, // Derived from store roleId
            isActive: typeof orgFromLocalList?.isActive === 'boolean' ? orgFromLocalList.isActive : (isOrgActive ?? false),
            org_member_is_active: typeof orgFromLocalList?.org_member_is_active === 'boolean' ? orgFromLocalList.org_member_is_active : (isUserActiveInOrg ?? false),
            org_icon: orgFromLocalList?.org_icon || null,
          };

          // Only update if there's a meaningful change to avoid unnecessary re-renders
          if (
            !prevSelectedOrg ||
            prevSelectedOrg.id !== newSelectedOrgData.id ||
            prevSelectedOrg.name !== newSelectedOrgData.name ||
            prevSelectedOrg.org_member_role !== newSelectedOrgData.org_member_role ||
            prevSelectedOrg.role !== newSelectedOrgData.role ||
            prevSelectedOrg.isActive !== newSelectedOrgData.isActive ||
            prevSelectedOrg.org_member_is_active !== newSelectedOrgData.org_member_is_active
          ) {
            console.log('[OrgSwitcher] Zustand store change triggered UI update for selectedOrg:', newSelectedOrgData.name, newSelectedOrgData.role);
            if (prevSelectedOrg?.id !== newSelectedOrgData.id) {
              previousOrgIdRef.current = newSelectedOrgData.id;
            }
            return newSelectedOrgData;
          }
          return prevSelectedOrg; // No change needed
        });
      }
    );

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, [localOrgs, isSwitching]); // Dependencies: localOrgs and isSwitching

  // Listen to Zustand store state changes directly (for initial load or if subscription somehow missed)
  React.useEffect(() => {
    // Get the current organization from the store
    const currentState = useAuthContextStore.getState();
    if (!currentState.orgId) return;

    // Check if we need to update the UI
    if (!selectedOrg || selectedOrg.id !== currentState.orgId) {
      // Find the organization in localOrgs
      const currentOrg = localOrgs.find(org => org.id === currentState.orgId);
      if (currentOrg) {
        console.log('[OrgSwitcher] Updating selectedOrg to match store state:', currentOrg.name);
        setSelectedOrg(currentOrg);
        previousOrgIdRef.current = currentState.orgId;
      }
    }
  }, [localOrgs, selectedOrg]);

  // The component now relies on the central useAuthContextEvents hook mounted in DashboardProvider
  // Individual event handlers remain for backward compatibility, but we don't subscribe
  // directly to AUTH_CONTEXT_CHANGED anymore to avoid redundant processing

  // Handle organization selection
  const handleOrganizationClick = React.useCallback((org: Organization) => {
    // The smart navigation logic is now handled inside switchOrganization
    // No need for special handling here - let the navigation decision system handle it
    switchOrganization(org.id);
  }, [switchOrganization]);

  // Show loading state during hydration to prevent flashes
  if (isStoreLoading) {
    return (
      <div className={cn("w-full relative", className)}>
        <div className="flex w-full items-center gap-2 rounded-lg p-2 bg-[#194852]/50 animate-pulse">
          <div className="h-8 w-8 rounded-lg bg-[#295D69]/50"></div>
          <div className="flex-1">
            <div className="h-4 bg-[#295D69]/50 rounded mb-1"></div>
            <div className="h-3 bg-[#295D69]/30 rounded w-2/3"></div>
          </div>
          <div className="h-4 w-4 bg-[#295D69]/30 rounded"></div>
        </div>
      </div>
    );
  }

  // If no organization is selected, try to use activeOrganization first before returning null
  if (!selectedOrg) {
    if (activeOrganization) {
      // If we have an active organization but no selected org, use the active org
      return (
        <div className={cn("w-full relative", className)}>
          <button
            className={cn(
              "flex w-full items-center gap-2 rounded-lg p-2 bg-[#194852] hover:bg-[#194852]",
              className
            )}
            onClick={() => setSelectedOrg(activeOrganization)}
          >
            <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-lg border border-[#295D69] bg-[#194852] overflow-hidden">
              {activeOrganization?.org_icon ? (
                <Image
                  src={activeOrganization.org_icon}
                  alt={activeOrganization.name}
                  width={32}
                  height={32}
                  className="object-cover"
                />
              ) : (
                <span className="text-white font-medium">
                  {activeOrganization?.name?.[0] || "?"}
                </span>
              )}
            </div>
            <div className="grid flex-1 text-left text-sm">
              <span className="font-semibold">{activeOrganization?.name}</span>
              <span className="text-xs text-muted-foreground">
                {typeof activeOrganization.org_member_role === 'number' ?
                  roleUtils.getRoleName(activeOrganization.org_member_role as any) : 'Member'}
              </span>
            </div>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </button>
        </div>
      );
    }
    return null;
  }

  return (
    <div
      className={cn(
        "w-full relative",
        className
      )}
    >
      <DropdownMenu modal={false} open={isOpen} onOpenChange={setIsOpen}>
        <DropdownMenuTrigger asChild disabled={isSwitching}>
          <button
            className={cn(
              "flex w-full items-center gap-2 rounded-lg p-2 bg-[#194852] hover:bg-[#194852]",
              isOpen && "bg-[#194852]",
              isSwitching && "cursor-not-allowed",
              className
            )}
          >
            <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-lg border border-[#295D69] bg-[#194852] overflow-hidden">
              {selectedOrg?.org_icon ? (
                <Image
                  src={selectedOrg.org_icon}
                  alt={selectedOrg.name}
                  width={32}
                  height={32}
                  className="object-cover"
                />
              ) : (
                <span className="text-white font-medium">
                  {selectedOrg?.name?.[0] || "?"}
                </span>
              )}
            </div>
            <div className="grid flex-1 text-left text-sm">
              <span className="font-semibold">{selectedOrg?.name}</span>
              <span className="text-xs text-muted-foreground">
                {selectedOrg?.org_member_role ?
                  roleUtils.getRoleName(selectedOrg.org_member_role as any) :
                  (selectedOrg?.role || 'Member')}
              </span>
            </div>
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </button>
        </DropdownMenuTrigger>
        <DropdownMenuContent
          className="w-[--radix-dropdown-menu-trigger-width] min-w-[16rem]"
          align="start"
          side="right"
          sideOffset={8}
        >
          <DropdownMenuLabel className="text-xs">Organizations</DropdownMenuLabel>
            {localOrgs
              // Filter out the currently selected org from dropdown
              .filter(org => org.id !== selectedOrg?.id)
              // Sort alphabetically by organization name (case-insensitive)
              .sort((a, b) => {
                const nameA = (a.name || '').toLowerCase();
                const nameB = (b.name || '').toLowerCase();
                return nameA.localeCompare(nameB);
              })
              .map((org) => {
                const isOrgDisabled = org.isActive === false;
                const isUserInactive = !org.org_member_is_active;

                return (
          <DropdownMenuItem
            key={org.id}
                onClick={() => handleOrganizationClick(org)}
            className={cn(
              "gap-2 p-2",
              // Grey out inactive organizations or disabled organizations
              (isOrgDisabled || isUserInactive) && "opacity-70"
            )}
          >
            <div className="flex">
              {/* Organization Icon */}
              <div className="flex h-8 w-8 shrink-0 items-center justify-center rounded-lg border bg-background">
                {org.org_icon ? (
                  <Image
                    src={org.org_icon}
                    alt={org.name || "Organization"}
                    width={32}
                    height={32}
                    className="object-cover"
                  />
                ) : (
                  <span className="font-medium">
                    {org.name?.[0] || "?"}
                  </span>
                )}
              </div>

              {/* Organization Name and Warning */}
              <div className="ml-3 grid gap-0.5">
                <div className="flex items-center gap-1">
                  <span className="font-medium">{org.name}</span>

                  {/* Show tooltip warning for inactive organization */}
                  {isOrgDisabled && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[200px]">
                          <p>This organization is disabled. Click to view.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}

                  {/* Show tooltip warning for inactive member */}
                  {isUserInactive && !isOrgDisabled && (
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger asChild>
                          <AlertCircle className="h-3.5 w-3.5 text-amber-500" />
                        </TooltipTrigger>
                        <TooltipContent className="max-w-[200px]">
                          <p>Your account is disabled in this organization. Click to view.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  )}
                </div>
                <span className="text-xs text-muted-foreground">
                  {isUserInactive ? "Disabled" :
                    (org.org_member_role ?
                      roleUtils.getRoleName(org.org_member_role as any) :
                      (org.role || "Member"))}
                </span>
              </div>
            </div>
          </DropdownMenuItem>
              );
            })}
          {selectedOrg?.org_member_role !== undefined && evaluateRbac(selectedOrg.org_member_role, { rRoles: ["superAdmin"] }) && (
            <>
              <DropdownMenuSeparator />
              <DropdownMenuItem
                onClick={() =>
                  router.push("/dashboard/developer/create-organization")
                }
                className="gap-2 p-2"
              >
                <div className="flex h-8 w-8 items-center justify-center rounded-lg border bg-background">
                  <Plus className="h-4 w-4" />
                </div>
                <span className="font-medium text-muted-foreground">
                  Create Organization
                </span>
              </DropdownMenuItem>
            </>
          )}
        </DropdownMenuContent>
      </DropdownMenu>
    </div>
  );
}
