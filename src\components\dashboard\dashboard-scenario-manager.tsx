'use client';

import { useEffect, useRef } from 'react';
import { useRouter } from 'next/navigation';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import { useServerContextRefresher } from '@/hooks/use-server-context-refresher';
import { evaluateUnifiedAccess } from '@/lib/navigation/route-access-evaluator';
import { useBusEvent } from '@/lib/useBusEvent';
import { refreshAuthContextThrottled } from '@/hooks/use-auth-context-events';
// Removed useAuthContextEvents import - it's already mounted in DashboardProvider
// Removed unused event imports since we're using the central event handler

// Unique instance ID for singleton management
const INSTANCE_ID = Math.random().toString(36).substring(2, 12);

// Configuration
const CONFIG = {
  SINGLETON_DOM_ID: 'dashboard-scenario-manager-singleton',
  DEBUG: false,
};

// Simple console logger with tag
function log(level: 'debug' | 'info' | 'warn' | 'error', ...args: unknown[]) {
  if (level === 'debug' && !CONFIG.DEBUG) return;
  console[level](`[DashboardScenarioManager ${INSTANCE_ID.slice(0, 4)}]`, ...args);
}

/**
 * DashboardScenarioManager
 *
 * This component handles ALL permission-affecting events and manages navigation
 * based on organization status, user status, role changes, and organization switches.
 * It ensures users are redirected to appropriate pages when their permissions change.
 *
 * Key responsibilities:
 * 1. Organization status changes → Redirect to org-disabled page
 * 2. User status changes → Redirect to account-disabled page
 * 3. Role changes → Redirect if page access lost
 * 4. Organization switches (cross-tab) → Redirect if page access lost
 * 5. Ensures only one instance handles events (singleton pattern)
 *
 * Note: User-initiated organization switches are handled by OrganizationSwitcher + PostNavigationOrgSwitch
 */
export function DashboardScenarioManager() {
  const router = useRouter();
  
  // Use the centralized server context refresher
  useServerContextRefresher();
  // Removed useAuthContextEvents() - it's already mounted in DashboardProvider
  
  // Track if this component is the active singleton instance
  const isSingletonRef = useRef(false);
  const previousRoleIdRef = useRef<number | null>(null);
  const previousOrgIdRef = useRef<string | null>(null);

  // Get active organization state from the store
  const { userId, orgId, roleId, isOrgActive, isUserActiveInOrg, isSuperAdmin } = useAuthContextStore();

  // Note: Account disabled and organization disabled logic is now handled
  // by the unified evaluation system in role change and organization switch handlers

  // Handle role changes - check if user still has access to current page
  useEffect(() => {
    // Skip if not in browser context
    if (typeof window === 'undefined') return;

    // Skip if we don't have all the required data
    if (roleId === null || isOrgActive === null || isUserActiveInOrg === null || orgId === null) return;

    // Check if role has changed WITHIN THE SAME ORGANIZATION
    const previousRoleId = previousRoleIdRef.current;
    const previousOrgId = previousOrgIdRef.current;

    // Only process role changes if we're in the same organization
    // This prevents duplicate processing when organization switches happen
    if (previousRoleId !== null && previousOrgId !== null &&
        previousOrgId === orgId && previousRoleId !== roleId) {

      const currentPath = window.location.pathname;
      log('info', `Role changed from ${previousRoleId} to ${roleId} in same org ${orgId} - checking page permissions for ${currentPath}`);

      // Use unified evaluation to handle all scenarios (account disabled, org disabled, route permissions)
      const unifiedResult = evaluateUnifiedAccess({
        currentPath,
        userRoleId: roleId,
        isUserActiveInOrg,
        isOrgActive,
        isSuperAdmin
      });

      log('info', 'Role change unified access evaluation:', {
        currentPath,
        oldRole: previousRoleId,
        newRole: roleId,
        canAccess: unifiedResult.canAccess,
        redirectTo: unifiedResult.redirectTo,
        priority: unifiedResult.priority,
        reason: unifiedResult.reason
      });

      // If user needs to be redirected (any priority except 'allowed')
      if (!unifiedResult.canAccess || unifiedResult.priority !== 'allowed') {
        log('info', `User needs redirect due to role change - redirecting to ${unifiedResult.redirectTo} (priority: ${unifiedResult.priority})`);

        // Set route-specific redirect flag to prevent unnecessary processing during redirect
        const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
        sessionStorage.setItem(redirectKey, 'true');
        setTimeout(() => {
          sessionStorage.removeItem(redirectKey);
        }, 1000);

        // Track recent redirect to prevent duplicate evaluations
        const recentRedirectKey = `recentRedirect_${currentPath}`;
        sessionStorage.setItem(recentRedirectKey, Date.now().toString());
        setTimeout(() => {
          sessionStorage.removeItem(recentRedirectKey);
        }, 5000); // Keep for 5 seconds

        router.push(unifiedResult.redirectTo);
        return;
      }

      log('debug', `User still has access to ${currentPath} with new role`);

      // Clear any redirect flags and allow normal updates since user has access
      const redirectKey = `dashboardRedirectInProgress_${currentPath}`;
      sessionStorage.removeItem(redirectKey);
    }

    // Update the previous references
    previousRoleIdRef.current = roleId;
    previousOrgIdRef.current = orgId;
  }, [roleId, orgId, isOrgActive, isUserActiveInOrg, isSuperAdmin, router]);

  // Handle organization switches AND role changes within current organization
  useBusEvent('db:organization_members:updated', (event) => {
    // Only process events for the current user
    if (event.userId !== userId) {
      return; // Event is for a different user
    }

    // Process two scenarios:
    // 1. Organization switches: !old_is_current_context && is_current_context
    // 2. Role changes in current org: old_is_current_context && is_current_context
    const isOrgSwitch = !event.data.old_is_current_context && event.data.is_current_context;
    const isRoleChangeInCurrentOrg = event.data.old_is_current_context && event.data.is_current_context;

    if (!isOrgSwitch && !isRoleChangeInCurrentOrg) {
      return; // Not a relevant event
    }

    if (isOrgSwitch) {
      // Handle organization switch logic
      const currentSwitchId = sessionStorage.getItem('currentOrgSwitchId');
      const isInitiatingTab = !!currentSwitchId;

      log('info', 'Checking organization switch origin', {
        currentSwitchId: currentSwitchId ? 'present' : 'none',
        isInitiatingTab,
        newOrgId: event.orgId
      });

      if (isInitiatingTab) {
        log('info', 'Skipping organization switch processing - this tab initiated the switch');
        return;
      }

      // Check if organization switcher already handled disabled accounts/orgs
      if (typeof window !== 'undefined') {
        const currentPath = window.location.pathname;
        if (currentPath === '/dashboard/account-disabled' || currentPath === '/dashboard/organization-disabled') {
          log('info', 'Skipping organization switch processing - already on disabled page, organization switcher handled it');
          return;
        }
      }

      log('info', 'Processing cross-tab organization switch', {
        newOrgId: event.orgId,
        orgName: event.data.org_name as string,
        roleId: event.data.org_member_role as number
      });
    } else if (isRoleChangeInCurrentOrg) {
      // Handle role change within current organization
      log('info', 'Processing role change in current organization', {
        orgId: event.orgId,
        orgName: event.data.org_name as string,
        newRole: event.data.org_member_role as number,
        oldRole: previousRoleIdRef.current
      });
    }

    // Skip if not in browser context
    if (typeof window === 'undefined') return;

    const currentPath = window.location.pathname;
    const redirectKey = `dashboardRedirectInProgress_${currentPath}`;

    // Use unified evaluation to handle all scenarios (account disabled, org disabled, route permissions)
    const unifiedResult = evaluateUnifiedAccess({
      currentPath,
      userRoleId: event.data.org_member_role as number,
      isUserActiveInOrg: event.data.org_member_is_active as boolean,
      isOrgActive: true, // Assume org is active if user has membership
      isSuperAdmin: (event.data.org_member_role as number) === 1
    });

    const eventType = isOrgSwitch ? 'organization switch' : 'role change';

    log('info', `${eventType} unified access evaluation:`, {
      currentPath,
      userRole: event.data.org_member_role as number,
      orgName: event.data.org_name as string,
      canAccess: unifiedResult.canAccess,
      redirectTo: unifiedResult.redirectTo,
      priority: unifiedResult.priority,
      reason: unifiedResult.reason
    });

    // Only set redirect flag if user will be redirected
    if (!unifiedResult.canAccess || unifiedResult.priority !== 'allowed') {
      log('info', `User needs redirect due to ${eventType} - redirecting to ${unifiedResult.redirectTo} (priority: ${unifiedResult.priority})`);

      // Set redirect flag to prevent unnecessary processing during redirect
      sessionStorage.setItem(redirectKey, 'true');
      setTimeout(() => {
        sessionStorage.removeItem(redirectKey);
      }, 1000);

      // Track recent redirect to prevent duplicate evaluations
      const recentRedirectKey = `recentRedirect_${currentPath}`;
      sessionStorage.setItem(recentRedirectKey, Date.now().toString());
      setTimeout(() => {
        sessionStorage.removeItem(recentRedirectKey);
      }, 5000); // Keep for 5 seconds

      router.push(unifiedResult.redirectTo);

      // Update previous role reference for role changes
      if (isRoleChangeInCurrentOrg) {
        previousRoleIdRef.current = event.data.org_member_role as number;
      }
    } else {
      log('debug', `User still has access to current page after ${eventType} - allowing normal updates`);

      // Ensure no redirect flag is set since user has access
      sessionStorage.removeItem(redirectKey);

      // Update previous role reference for role changes
      if (isRoleChangeInCurrentOrg) {
        previousRoleIdRef.current = event.data.org_member_role as number;
      }

      // Trigger immediate auth context refresh for successful organization switch
      if (isOrgSwitch) {
        log('debug', 'Triggering auth context refresh for successful organization switch');
        refreshAuthContextThrottled();
      }
    }
  }, [router]);

  // Clean up old redirect flags when navigating to new routes
  useEffect(() => {
    if (typeof window === 'undefined') return;

    // Clean up any old redirect flags and recent redirect tracking for other routes
    const currentPath = window.location.pathname;
    const allKeys = Object.keys(sessionStorage);
    allKeys.forEach(key => {
      if ((key.startsWith('dashboardRedirectInProgress_') && key !== `dashboardRedirectInProgress_${currentPath}`) ||
          (key.startsWith('recentRedirect_') && key !== `recentRedirect_${currentPath}`)) {
        sessionStorage.removeItem(key);
      }
    });
  }, [typeof window !== 'undefined' ? window.location.pathname : '']);

  // Set up a singleton marker in the DOM
  useEffect(() => {
    if (typeof window === 'undefined') return; // Only run on client
    
    // Check if this instance should be the singleton
    const isNowSingleton = (() => {
      const marker = document.getElementById(CONFIG.SINGLETON_DOM_ID);
      
      if (!marker) {
        // No marker exists, create one and make this instance the singleton
        const newMarker = document.createElement('div');
        newMarker.id = CONFIG.SINGLETON_DOM_ID;
        newMarker.setAttribute('data-instance-id', INSTANCE_ID);
        newMarker.setAttribute('data-status', 'ready');
        newMarker.style.display = 'none';
        document.body.appendChild(newMarker);
        return true;
      }
      
      // Marker already exists, check if it belongs to this instance
      return marker.getAttribute('data-instance-id') === INSTANCE_ID;
    })();
    
    // Update the singleton status
    isSingletonRef.current = isNowSingleton;
    
    if (!isNowSingleton) {
      log('info', `Instance ${INSTANCE_ID} is NOT the singleton.`);
      return;
    }
    
    log('info', `Instance ${INSTANCE_ID} is now the active singleton.`);
    
    // We no longer need a direct AUTH_CONTEXT_CHANGED listener here
    // The useAuthContextEvents hook (called above) already handles refreshing the context
    // when AUTH_CONTEXT_CHANGED events occur, and the useEffect watching the auth context
    // state will handle the navigation logic.
    
    return () => {
      
      // Clean up singleton marker if appropriate
      if (isSingletonRef.current) {
        const marker = document.getElementById(CONFIG.SINGLETON_DOM_ID);
        if (marker && marker.getAttribute('data-instance-id') === INSTANCE_ID) {
          marker.remove();
          log('info', `Singleton instance ${INSTANCE_ID} cleanup: Removing DOM marker.`);
        }
      }
    };
  }, [userId]); // Only re-run if userId changes

  return null;
}
