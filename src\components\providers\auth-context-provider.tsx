'use client';

import { useEffect } from 'react';
import { useAuthContextStore } from '@/stores/useAuthContextStore';
import type { ApiRefreshedAuthContext } from '@/hooks/use-server-context-refresher';

export function AuthContextProvider({
  initialContext,
  children,
}: {
  initialContext: ApiRefreshedAuthContext;
  children: React.ReactNode;
}) {
  const hydrate = useAuthContextStore((state) => state.updateFullContext);

  // Hydrate the store with server-side data on mount
  useEffect(() => {
    const storeState = useAuthContextStore.getState();
    
    // Check if we're in an optimistic navigation or update process
    if (storeState.optimisticNavigation || storeState.optimisticLoading) {
      console.debug('[AuthContextProvider] Optimistic navigation in progress, preserving client state and skipping hydration');
      return; // Skip hydration during optimistic updates to prevent flicker/override
    }
    
    // Normal hydration flow if not optimisticLoading or if optimistic state is old
    if (process.env.NODE_ENV === 'development') {
      console.debug('[AuthContextProvider] Hydrating store with initial context:', initialContext);
    }
    
    // Only update if we have actual data to hydrate with
    if (initialContext) {
      hydrate(initialContext);
    }
  }, [hydrate, initialContext]);

  return <>{children}</>;
}
